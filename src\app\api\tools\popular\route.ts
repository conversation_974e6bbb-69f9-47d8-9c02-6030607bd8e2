import { createGetHand<PERSON> } from '@/lib/utils/api-handler';
import { successResponse } from '@/lib/utils/api-responses';
import { z } from 'zod';
import apiClient from '@/lib/api-client';
import { Role, Tool } from '@prisma/client';
import { API_ENDPOINTS } from '@/lib/constants/api';
import { PERMISSIONS } from '@/lib/constants/permissions';
import { AuthenticatedRequest, ValidatedRequest } from '@/lib/middleware/auth.middleware';

// Query validation schema
const querySchema = z.object({
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
});

// GET /api/tools/popular - Get popular tools
export const GET = createGetHandler(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const { limit } = req.validatedQuery;



    // Build query parameters
    const queryParams = new URLSearchParams({ limit: limit.toString() });
    const tools = await apiClient.get(`${API_ENDPOINTS.TOOLS.POPULAR}?${queryParams.toString()}`) as Tool[];
    return successResponse(tools, "Popular tools fetched successfully");
  },    
  {
    querySchema,  
    requiredPermissions: [PERMISSIONS.TOOL_READ],
    requiredRoles: ['SUPER_ADMIN' as Role],
  }
);