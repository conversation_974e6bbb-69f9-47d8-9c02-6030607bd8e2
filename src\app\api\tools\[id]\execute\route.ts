import { createPost<PERSON><PERSON><PERSON> } from '@/lib/utils/api-handler';
import { successResponse } from '@/lib/utils/api-responses';
import { z } from 'zod';
import { Permissions } from '@/lib/types/auth';
import apiClient from '@/lib/api-client';

// Validation schema for params
const paramsSchema = z.object({
  id: z.string().uuid('Invalid tool ID'),
});

// Tool execution schema
const executeToolSchema = z.object({
  input: z.record(z.string(), z.any()),
  sessionId: z.string().optional(),
  timeout: z.number().min(1000).max(300000).optional(), // 1s to 5min
  metadata: z.record(z.string(), z.any()).optional(),
});

// POST /api/tools/[id]/execute - Execute a specific tool
export const POST = createPostHandler(
  async (req) => {
    const { id } = req.validatedParams;
    const executionData = req.validatedBody;

    const result = await apiClient.post(`/tools/${id}/execute`, executionData);
    return successResponse(result, "Tool executed successfully");
  },
  {
    paramsSchema,
    bodySchema: executeToolSchema,
    requiredPermissions: [Permissions.TOOL_EXECUTE],
  }
);