import { NextRequest, NextResponse } from 'next/server';
import apiClient from '@/lib/api-client';
import { ValidatedRequest, AuthenticatedRequest, withAuthAndValidation } from '@/lib/middleware/auth.middleware';
import { z } from 'zod';
import { API_ENDPOINTS } from '@/lib/constants/api';
import { PERMISSIONS } from '@/lib/constants/permissions';

const getToolsSchema = z.object({
  query: z.string().optional(),
  type: z.string().optional(),
  category: z.string().optional(),
  tags: z.string().optional(),
  isPublic: z.string().optional().transform(val => val === 'true'),
  page: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  limit: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  sortBy: z.string().optional(),
  sortOrder: z.string().optional(),
});

const createToolSchema = z.object({
  name: z.string(),
  description: z.string(),
  type: z.string(),
  category: z.string(),
  tags: z.array(z.string()),
  isPublic: z.boolean(),
});

const updateToolSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  description: z.string().optional(),
  type: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
});

// HTTP method handlers for Next.js App Router
export async function GET(req: NextRequest) {
  const handler = withAuthAndValidation(
    async (req: AuthenticatedRequest & ValidatedRequest) => {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (req.validatedQuery.query) queryParams.append('query', req.validatedQuery.query);
      if (req.validatedQuery.type) queryParams.append('type', req.validatedQuery.type);
      if (req.validatedQuery.category) queryParams.append('category', req.validatedQuery.category);
      if (req.validatedQuery.tags) queryParams.append('tags', req.validatedQuery.tags);
      if (req.validatedQuery.isPublic !== undefined) queryParams.append('isPublic', req.validatedQuery.isPublic.toString());
      if (req.validatedQuery.page) queryParams.append('page', req.validatedQuery.page.toString());
      if (req.validatedQuery.limit) queryParams.append('limit', req.validatedQuery.limit.toString());
      if (req.validatedQuery.sortBy) queryParams.append('sortBy', req.validatedQuery.sortBy);
      if (req.validatedQuery.sortOrder) queryParams.append('sortOrder', req.validatedQuery.sortOrder);

      const tools = await apiClient.get(`${API_ENDPOINTS.TOOLS.BASE}?${queryParams.toString()}`);
      return NextResponse.json(tools);
    },
    {
      requiredRoles: ['SUPER_ADMIN'],
      requiredPermissions: [PERMISSIONS.TOOL_READ],
    },
    {
      query: getToolsSchema,
    }
  );

  return handler(req);
}

export async function POST(req: NextRequest) {
  const handler = withAuthAndValidation(
    async (req: AuthenticatedRequest & ValidatedRequest) => {
      const tool = await apiClient.post(API_ENDPOINTS.TOOLS.BASE, req.validatedBody);
      return NextResponse.json(tool);
    },
    {
      requiredRoles: ['SUPER_ADMIN'],
      requiredPermissions: [PERMISSIONS.TOOL_CREATE],
    },
    {
      body: createToolSchema,
    }
  );

  return handler(req);
}

export async function PUT(req: NextRequest) {
  const handler = withAuthAndValidation(
    async (req: AuthenticatedRequest & ValidatedRequest) => {
      const tool = await apiClient.put(API_ENDPOINTS.TOOLS.BASE, req.validatedBody);
      return NextResponse.json(tool);
    },
    {
      requiredRoles: ['SUPER_ADMIN'],
      requiredPermissions: [PERMISSIONS.TOOL_UPDATE],
    },
    {
      body: updateToolSchema,
    }
  );

  return handler(req);
}

export async function DELETE(req: NextRequest) {
  const handler = withAuthAndValidation(
    async (request: NextRequest) => {
      const { searchParams } = new URL(request.url);
      const id = searchParams.get('id');
      const tool = await apiClient.delete(`${API_ENDPOINTS.TOOLS.BASE}/${id}`);
      return NextResponse.json(tool);
    },
    {
      requiredRoles: ['SUPER_ADMIN'],
      requiredPermissions: [PERMISSIONS.TOOL_DELETE],
    }
  );

  return handler(req);
}