/**
 * Session API Route
 * Production-grade session endpoint for client-side authentication
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedSession } from '@/lib/auth/auth-service';

export async function GET(req: NextRequest): Promise<NextResponse> {
    try {
        // Get authenticated session
        const session = await getAuthenticatedSession(req);

        if (!session) {
            return NextResponse.json({
                user: null,
                isAuthenticated: false
            });
        }

        return NextResponse.json({
            user: session.user,
            isAuthenticated: true,
            expiresAt: session.expiresAt
        });
    } catch (error: any) {
        console.error('Session error:', error);

        return NextResponse.json({
            user: null,
            isAuthenticated: false,
            error: error.message || 'Session verification failed'
        });
    }
}