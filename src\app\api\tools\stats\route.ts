import { createGetHand<PERSON> } from '@/lib/utils/api-handler';
import { successResponse } from '@/lib/utils/api-responses';
import { Role } from '@prisma/client';
import { PERMISSIONS } from '@/lib/constants/permissions';
import { AuthenticatedRequest, ValidatedRequest } from '@/lib/middleware/auth.middleware';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';


const querySchema = z.object({
  timeRange: z.enum(['7d', '30d', '90d', '180d', '365d']).optional(),
});

const prisma = new PrismaClient();


// GET /api/tools/stats - Get general tool statistics
export const GET = createGetHandler(
  async (req: AuthenticatedRequest & ValidatedRequest) => {
    const { timeRange } = req.validatedQuery;
    const stats = await prisma.tool.aggregate({
      _count: {
        _all: true,
      },
      where: {
        createdAt: {
          gte: new Date(Date.now() - (timeRange ? parseInt(timeRange) : 7) * 24 * 60 * 60 * 1000),
        },
      },
    });
    return successResponse(stats, "Tool statistics fetched successfully");
  },

  {
    requiredPermissions: [PERMISSIONS.TOOL_READ],
    requiredRoles: ['SUPER_ADMIN' as Role],
    querySchema,
  }
);


