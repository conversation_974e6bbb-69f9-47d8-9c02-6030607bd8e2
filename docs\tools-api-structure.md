# 🔧 Tools API Structure & Alignment

This document outlines the complete Tools API structure, its alignment with backend services, and the centralized patterns implemented.

## 📊 API Endpoint Overview

### **Core CRUD Operations**
```
GET    /api/tools              - List tools with filtering/pagination
POST   /api/tools              - Create a new tool
GET    /api/tools/[id]         - Get specific tool details
PATCH  /api/tools/[id]         - Update specific tool
DELETE /api/tools/[id]         - Delete specific tool
```

### **Discovery & Analytics**
```
GET    /api/tools/popular      - Get popular tools (by usage)
GET    /api/tools/stats        - Get general tool statistics
GET    /api/tools/categories   - Get tool categories
POST   /api/tools/categories   - Create new category
GET    /api/tools/[id]/stats   - Get specific tool analytics
```

### **Execution & Testing**
```
POST   /api/tools/[id]/execute    - Execute a tool
GET    /api/tools/[id]/executions - Get execution history
POST   /api/tools/[id]/test       - Test a tool
```

## 🎯 Backend Service Alignment

### **Backend Endpoints (NestJS)**
The frontend routes map directly to backend services:

| Frontend Route | Backend Endpoint | Service Method |
|---|---|---|
| `GET /api/tools` | `GET /tools` | `toolsService.searchTools()` |
| `GET /api/tools/popular` | `GET /tools/popular` | `toolsService.getPopularTools()` |
| `GET /api/tools/stats` | `GET /tools/stats` | `toolsService.getToolStats()` |
| `GET /api/tools/categories` | `GET /tools/categories` | `toolsService.getCategories()` |
| `POST /api/tools/[id]/execute` | `POST /tools/:id/execute` | `toolsService.executeTool()` |
| `GET /api/tools/[id]/stats` | `GET /tools/:id/analytics` | `toolsService.getToolAnalytics()` |

### **Backend Services Architecture**
```
ToolsService
├── ToolManagerService      - CRUD operations, search, discovery
├── ToolExecutionService    - Tool execution engine
├── ToolAnalyticsService    - Usage analytics, performance metrics
└── ToolCacheService        - Caching, optimization
```

## ✅ Implemented Patterns

### **1. Centralized Authentication & Authorization**
All routes now use the `createApiHandler` pattern:

```typescript
export const GET = createGetHandler(
  async (req) => {
    // Handler logic
  },
  {
    querySchema,
    requiredPermissions: [Permissions.TOOL_READ],
  }
);
```

### **2. Consistent Validation**
Zod schemas for all inputs:

```typescript
const querySchema = z.object({
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  category: z.string().optional(),
});

const executeToolSchema = z.object({
  input: z.record(z.string(), z.any()),
  timeout: z.number().min(1000).max(300000).optional(),
});
```

### **3. Standardized Responses**
Consistent response format using helper functions:

```typescript
return successResponse(data, "Operation completed successfully");
return createdResponse(newResource, "Resource created successfully");
```

### **4. Proper Error Handling**
Automatic error handling through the centralized handler:
- Validation errors → 400 with detailed field errors
- Authentication errors → 401
- Permission errors → 403
- Not found errors → 404
- Server errors → 500

## 🔄 Migration Status

### **✅ Completed Migrations**
- `/api/tools/route.ts` - Core CRUD operations
- `/api/tools/[id]/route.ts` - Individual tool operations
- `/api/tools/popular/route.ts` - Popular tools endpoint
- `/api/tools/stats/route.ts` - General statistics
- `/api/tools/categories/route.ts` - Category management
- `/api/tools/[id]/execute/route.ts` - Tool execution
- `/api/tools/[id]/stats/route.ts` - Individual tool analytics (NEW)

### **🔄 Remaining Routes**
- `/api/tools/[id]/executions/route.ts` - Execution history
- `/api/tools/[id]/test/route.ts` - Tool testing

## 📈 Analytics & Metrics

### **Tool Analytics Structure**
The backend provides comprehensive analytics through `ToolAnalyticsService`:

```typescript
interface ToolAnalytics {
  executionCount: number;
  successCount: number;
  errorCount: number;
  avgDuration: number;
  totalDuration: number;
  cacheHitRate: number;
  popularityScore: number;
  performanceMetrics: {
    p50Duration: number;
    p95Duration: number;
    p99Duration: number;
  };
}
```

### **Popular Tools Algorithm**
Tools are ranked by:
1. **Execution frequency** (30-day window)
2. **Success rate** (weighted)
3. **User adoption** (unique users)
4. **Recent activity** (recency boost)

## 🔐 Permission Model

### **Tool Permissions**
```typescript
const Permissions = {
  TOOL_READ: 'tool:read',
  TOOL_CREATE: 'tool:create', 
  TOOL_UPDATE: 'tool:update',
  TOOL_DELETE: 'tool:delete',
  TOOL_EXECUTE: 'tool:execute',
};
```

### **Role-Based Access**
- **VIEWER**: Can read and execute tools
- **DEVELOPER**: Can create, update, and manage tools
- **ORG_ADMIN**: Full tool management within organization
- **SUPER_ADMIN**: Global tool management

## 🚀 Performance Optimizations

### **Caching Strategy**
1. **Popular tools** - Cached for 15 minutes
2. **Tool categories** - Cached for 1 hour
3. **Tool statistics** - Cached for 5 minutes
4. **Execution results** - Cached based on input hash

### **Query Optimization**
- Pagination for all list endpoints
- Efficient database queries with proper indexing
- Aggregated statistics computed in background jobs

## 🧪 Testing Strategy

### **API Testing**
Each endpoint should have tests covering:
- Authentication/authorization
- Input validation
- Success scenarios
- Error scenarios
- Edge cases

### **Example Test Structure**
```typescript
describe('/api/tools/popular', () => {
  it('should return popular tools for authenticated user', async () => {
    // Test implementation
  });
  
  it('should respect limit parameter', async () => {
    // Test implementation
  });
  
  it('should return 401 for unauthenticated request', async () => {
    // Test implementation
  });
});
```

## 📝 Next Steps

1. **Complete remaining route migrations**
2. **Add comprehensive test coverage**
3. **Implement rate limiting for execution endpoints**
4. **Add real-time analytics dashboard**
5. **Optimize caching strategies**
6. **Add tool composition/workflow features**

## 🔗 Related Documentation

- [API Best Practices Guide](./api-best-practices.md)
- [Authentication & Authorization](./auth-patterns.md)
- [Error Handling Patterns](./error-handling.md)
- [Testing Guidelines](./testing-guidelines.md)
