/**
 * Permission Constants
 * Centralized permission definitions for consistent usage across the application
 */

import { Role } from '@prisma/client';

/**
 * All available permissions in the system
 */
export const PERMISSIONS = {
    // User management
    USER_CREATE: 'user:create',
    USER_READ: 'user:read',
    USER_UPDATE: 'user:update',
    USER_DELETE: 'user:delete',
    USER_SETTINGS_READ: 'user:settings:read',
    USER_SETTINGS_UPDATE: 'user:settings:update',

    // Organization management
    ORG_CREATE: 'org:create',
    ORG_READ: 'org:read',
    ORG_UPDATE: 'org:update',
    ORG_DELETE: 'org:delete',
    ORG_SETTINGS_READ: 'org:settings:read',
    ORG_SETTINGS_UPDATE: 'org:settings:update',

    // Tool management
    TOOL_CREATE: 'tool:create',
    TOOL_READ: 'tool:read',
    TOOL_UPDATE: 'tool:update',
    TOOL_DELETE: 'tool:delete',
    TOOL_EXECUTE: 'tool:execute',
    TOOL_ANALYTICS_READ: 'tool:analytics:read',
    TOOL_ANALYTICS_UPDATE: 'tool:analytics:update',

    // Agent management
    AGENT_CREATE: 'agent:create',
    AGENT_READ: 'agent:read',
    AGENT_UPDATE: 'agent:update',
    AGENT_DELETE: 'agent:delete',
    AGENT_EXECUTE: 'agent:execute',
    AGENT_ANALYTICS_READ: 'agent:analytics:read',
    AGENT_ANALYTICS_UPDATE: 'agent:analytics:update',

    // Workflow management
    WORKFLOW_CREATE: 'workflow:create',
    WORKFLOW_READ: 'workflow:read',
    WORKFLOW_UPDATE: 'workflow:update',
    WORKFLOW_DELETE: 'workflow:delete',
    WORKFLOW_EXECUTE: 'workflow:execute',
    WORKFLOW_ANALYTICS_READ: 'workflow:analytics:read',
    WORKFLOW_ANALYTICS_UPDATE: 'workflow:analytics:update',
    WORKFLOW_EXECUTION_READ: 'workflow:execution:read',
    WORKFLOW_EXECUTION_UPDATE: 'workflow:execution:update',

    // Billing management
    BILLING_READ: 'billing:read',
    BILLING_UPDATE: 'billing:update',

    // Analytics
    ANALYTICS_VIEW: 'analytics:view',
    ANALYTICS_READ: 'analytics:read',
    ANALYTICS_UPDATE: 'analytics:update',

    // Settings
    SETTINGS_READ: 'settings:read',
    SETTINGS_UPDATE: 'settings:update',

    // API keys
    API_KEY_CREATE: 'api-key:create',
    API_KEY_READ: 'api-key:read',
    API_KEY_DELETE: 'api-key:delete'
};

/**
 * Default permissions by role
 */
export const DEFAULT_PERMISSIONS: Record<Role, string[]> = {
    'SUPER_ADMIN': Object.values(PERMISSIONS),
    'ORG_ADMIN': [
        PERMISSIONS.USER_CREATE, PERMISSIONS.USER_READ, PERMISSIONS.USER_UPDATE, PERMISSIONS.USER_DELETE,
        PERMISSIONS.USER_SETTINGS_READ, PERMISSIONS.USER_SETTINGS_UPDATE,
        PERMISSIONS.ORG_READ, PERMISSIONS.ORG_UPDATE, PERMISSIONS.ORG_SETTINGS_READ,
        PERMISSIONS.ORG_SETTINGS_UPDATE, PERMISSIONS.ORG_DELETE,
        PERMISSIONS.TOOL_CREATE, PERMISSIONS.TOOL_READ, PERMISSIONS.TOOL_UPDATE,
        PERMISSIONS.TOOL_DELETE, PERMISSIONS.TOOL_EXECUTE, PERMISSIONS.TOOL_ANALYTICS_READ,
        PERMISSIONS.TOOL_ANALYTICS_UPDATE,
        PERMISSIONS.AGENT_CREATE, PERMISSIONS.AGENT_READ, PERMISSIONS.AGENT_UPDATE,
        PERMISSIONS.AGENT_DELETE, PERMISSIONS.AGENT_EXECUTE, PERMISSIONS.AGENT_ANALYTICS_READ,
        PERMISSIONS.AGENT_ANALYTICS_UPDATE,
        PERMISSIONS.WORKFLOW_CREATE, PERMISSIONS.WORKFLOW_READ, PERMISSIONS.WORKFLOW_UPDATE,
        PERMISSIONS.WORKFLOW_DELETE, PERMISSIONS.WORKFLOW_EXECUTE, PERMISSIONS.WORKFLOW_ANALYTICS_READ,
        PERMISSIONS.WORKFLOW_ANALYTICS_UPDATE,
        PERMISSIONS.WORKFLOW_EXECUTION_READ, PERMISSIONS.WORKFLOW_EXECUTION_UPDATE,
        PERMISSIONS.BILLING_READ, PERMISSIONS.BILLING_UPDATE,
        PERMISSIONS.ANALYTICS_VIEW, PERMISSIONS.ANALYTICS_READ, PERMISSIONS.ANALYTICS_UPDATE,
        PERMISSIONS.SETTINGS_READ, PERMISSIONS.SETTINGS_UPDATE,
        PERMISSIONS.API_KEY_CREATE, PERMISSIONS.API_KEY_READ, PERMISSIONS.API_KEY_DELETE
    ],
    'ADMIN': [
        PERMISSIONS.USER_CREATE, PERMISSIONS.USER_READ, PERMISSIONS.USER_UPDATE, PERMISSIONS.USER_DELETE,
        PERMISSIONS.USER_SETTINGS_READ, PERMISSIONS.USER_SETTINGS_UPDATE,
        PERMISSIONS.ORG_READ, PERMISSIONS.ORG_UPDATE, PERMISSIONS.ORG_SETTINGS_READ,
        PERMISSIONS.ORG_SETTINGS_UPDATE, PERMISSIONS.ORG_DELETE,
        PERMISSIONS.TOOL_CREATE, PERMISSIONS.TOOL_READ, PERMISSIONS.TOOL_UPDATE,
        PERMISSIONS.TOOL_DELETE, PERMISSIONS.TOOL_EXECUTE, PERMISSIONS.TOOL_ANALYTICS_READ,
        PERMISSIONS.TOOL_ANALYTICS_UPDATE,
        PERMISSIONS.AGENT_CREATE, PERMISSIONS.AGENT_READ, PERMISSIONS.AGENT_UPDATE,
        PERMISSIONS.AGENT_DELETE, PERMISSIONS.AGENT_EXECUTE, PERMISSIONS.AGENT_ANALYTICS_READ,
        PERMISSIONS.AGENT_ANALYTICS_UPDATE,
        PERMISSIONS.WORKFLOW_CREATE, PERMISSIONS.WORKFLOW_READ, PERMISSIONS.WORKFLOW_UPDATE,
        PERMISSIONS.WORKFLOW_DELETE, PERMISSIONS.WORKFLOW_EXECUTE, PERMISSIONS.WORKFLOW_ANALYTICS_READ,
        PERMISSIONS.WORKFLOW_ANALYTICS_UPDATE,
        PERMISSIONS.WORKFLOW_EXECUTION_READ, PERMISSIONS.WORKFLOW_EXECUTION_UPDATE,
        PERMISSIONS.ANALYTICS_VIEW, PERMISSIONS.ANALYTICS_READ, PERMISSIONS.ANALYTICS_UPDATE,
        PERMISSIONS.SETTINGS_READ, PERMISSIONS.SETTINGS_UPDATE,
        PERMISSIONS.API_KEY_CREATE, PERMISSIONS.API_KEY_READ, PERMISSIONS.API_KEY_DELETE
    ],
   
    'DEVELOPER': [
        PERMISSIONS.USER_READ,
        PERMISSIONS.ORG_READ,
        PERMISSIONS.TOOL_CREATE, PERMISSIONS.TOOL_READ, PERMISSIONS.TOOL_UPDATE,
        PERMISSIONS.TOOL_EXECUTE, PERMISSIONS.TOOL_ANALYTICS_READ,
        PERMISSIONS.AGENT_CREATE, PERMISSIONS.AGENT_READ, PERMISSIONS.AGENT_UPDATE,
        PERMISSIONS.AGENT_EXECUTE, PERMISSIONS.AGENT_ANALYTICS_READ,
        PERMISSIONS.WORKFLOW_CREATE, PERMISSIONS.WORKFLOW_READ, PERMISSIONS.WORKFLOW_UPDATE,
        PERMISSIONS.WORKFLOW_EXECUTE, PERMISSIONS.WORKFLOW_ANALYTICS_READ,
        PERMISSIONS.WORKFLOW_EXECUTION_READ, PERMISSIONS.WORKFLOW_EXECUTION_UPDATE,
        PERMISSIONS.ANALYTICS_VIEW, PERMISSIONS.ANALYTICS_READ,
        PERMISSIONS.SETTINGS_READ,
        PERMISSIONS.API_KEY_CREATE, PERMISSIONS.API_KEY_READ
    ],
    'VIEWER': [
        PERMISSIONS.USER_READ,
        PERMISSIONS.ORG_READ,
        PERMISSIONS.TOOL_READ, PERMISSIONS.TOOL_EXECUTE,
        PERMISSIONS.AGENT_READ, PERMISSIONS.AGENT_EXECUTE,
        PERMISSIONS.WORKFLOW_READ, PERMISSIONS.WORKFLOW_EXECUTE,
        PERMISSIONS.WORKFLOW_EXECUTION_READ,
        PERMISSIONS.ANALYTICS_VIEW
    ],
    'USER': [
        PERMISSIONS.USER_READ,
        PERMISSIONS.TOOL_READ, PERMISSIONS.TOOL_EXECUTE,
        PERMISSIONS.AGENT_READ, PERMISSIONS.AGENT_EXECUTE,
        PERMISSIONS.WORKFLOW_READ, PERMISSIONS.WORKFLOW_EXECUTE
    ]
};

/**
 * Check if a user has a specific permission
 */
export function hasPermission(user: { role: Role, permissions: string[] }, permission: string): boolean {
    // Super admin and org admin always have all permissions
    if (['SUPER_ADMIN', 'ORG_ADMIN', 'ADMIN'].includes(user.role)) {
        return true;
    }

    // Check user's explicit permissions
    return user.permissions.includes(permission);
}

/**
 * Check if a user has all specified permissions
 */
export function hasAllPermissions(user: { role: Role, permissions: string[] }, permissions: string[]): boolean {
    return permissions.every(permission => hasPermission(user, permission));
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(user: { role: Role, permissions: string[] }, permissions: string[]): boolean {
    return permissions.some(permission => hasPermission(user, permission));
}