/**
 * APIX Client Wrapper
 * A centralized wrapper for the APIX client to ensure consistent usage across the application
 */

import { APXClient, APXEventType, APXEvent, APXConfig } from './apix-client';
import { APIX_EVENT_TYPES, APIX_CHANNELS, DEFAULT_APIX_CONFIG } from './constants/events';
import { APXChannel } from './apix-client';

// Singleton instance
let apixClientInstance: APXClient | null = null;

/**
 * Get the APIX client instance (singleton pattern)
 */
export function getApixClient(): APXClient {
    if (!apixClientInstance) {
        apixClientInstance = createApixClient();
    }
    return apixClientInstance;
}

/**
 * Create a new APIX client with default configuration
 */
export function createApixClient(config: Partial<APXConfig> = {}): APXClient {
    const mergedConfig = {
        ...DEFAULT_APIX_CONFIG,
        ...config
    };

    const url = process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:3001';
    return new APXClient(url, {}, mergedConfig);
}

/**
 * Connect to the APIX server
 */
export async function connectApix(token: string, organizationId: string): Promise<void> {
    const client = getApixClient();
    return client.connect(token, organizationId);
}

/**
 * Disconnect from the APIX server
 */
export function disconnectApix(): void {
    const client = getApixClient();
    client.disconnect();
}

/**
 * Subscribe to an event type
 */
export function subscribeToEvent(
    eventType: APXEventType | keyof typeof APIX_EVENT_TYPES,
    handler: (event: APXEvent) => void
): () => void {
    const client = getApixClient();
    // Convert string constant to actual event type if needed
    const actualEventType = APIX_EVENT_TYPES[eventType as keyof typeof APIX_EVENT_TYPES] || eventType;
    return client.on(actualEventType as APXEventType, handler);
}

/**
 * Unsubscribe from an event type
 */
export function unsubscribeFromEvent(
    eventType: APXEventType | keyof typeof APIX_EVENT_TYPES,
    handler?: (event: APXEvent) => void
): void {
    const client = getApixClient();
    // Convert string constant to actual event type if needed
    const actualEventType = APIX_EVENT_TYPES[eventType as keyof typeof APIX_EVENT_TYPES] || eventType;
    client.off(actualEventType as APXEventType, handler);
}

/**
 * Emit an event
 */
export function emitEvent(
    eventType: APXEventType | keyof typeof APIX_EVENT_TYPES,
    data: any,
    options: {
        compress?: boolean;
        retry?: boolean;
        maxRetries?: number;
        priority?: 'low' | 'normal' | 'high';
        channel?: APXChannel;
        metadata?: Record<string, any>;
    } = {}
): void {
    const client = getApixClient();
    // Convert string constant to actual event type if needed
    const actualEventType = APIX_EVENT_TYPES[eventType as keyof typeof APIX_EVENT_TYPES] || eventType;
    client.emit(actualEventType as APXEventType, data, {
        channel: options.channel as APXChannel,
        ...options
    });
}

/**
 * Subscribe to a channel
 */
export function subscribeToChannel(channel: string): void {
    const client = getApixClient();

    switch (channel) {
        case APIX_CHANNELS.AGENT_EVENTS:
            client.subscribeToEventType(APIX_EVENT_TYPES.AGENT_STATUS as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.AGENT_THINKING as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.AGENT_RESPONSE as APXEventType);
            break;
        case APIX_CHANNELS.TOOL_EVENTS:
            client.subscribeToEventType(APIX_EVENT_TYPES.TOOL_CALL_START as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.TOOL_CALL_RESULT as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.TOOL_CALL_ERROR as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.TOOL_EXECUTED as APXEventType);
            break;
        case APIX_CHANNELS.WORKFLOW_EVENTS:
            client.subscribeToEventType(APIX_EVENT_TYPES.WORKFLOW_STARTED as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.WORKFLOW_COMPLETED as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.WORKFLOW_FAILED as APXEventType);
            break;
        case APIX_CHANNELS.SYSTEM_EVENTS:
            client.subscribeToEventType(APIX_EVENT_TYPES.SYSTEM_ALERT as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.SYSTEM_MAINTENANCE as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.ERROR_OCCURRED as APXEventType);
            break;
        case APIX_CHANNELS.ADMIN_EVENTS:
            client.subscribeToEventType(APIX_EVENT_TYPES.ADMIN_EVENT as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.ADMIN_USER_CREATED as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.ADMIN_USER_UPDATED as APXEventType);
            break;
        case APIX_CHANNELS.ORGANIZATION_EVENTS:
            client.subscribeToEventType(APIX_EVENT_TYPES.ORGANIZATION_EVENT as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.ORGANIZATION_CREATED as APXEventType);
            client.subscribeToEventType(APIX_EVENT_TYPES.ORGANIZATION_UPDATED as APXEventType);
            break;
        default:
            // For custom channels, just subscribe directly
            client.joinRoom(channel);
    }
}

/**
 * Get connection status
 */
export function getApixConnectionStatus() {
    const client = getApixClient();
    return client.getConnectionStatus();
}

/**
 * Get connection metrics
 */
export function getApixMetrics() {
    const client = getApixClient();
    return client.getConnectionMetrics();
}

/**
 * Perform a health check
 */
export async function checkApixHealth() {
    const client = getApixClient();
    return client.performHealthCheck();
}

// Default export for convenience
export default {
    getClient: getApixClient,
    connect: connectApix,
    disconnect: disconnectApix,
    subscribe: subscribeToEvent,
    unsubscribe: unsubscribeFromEvent,
    emit: emitEvent,
    subscribeToChannel,
    getStatus: getApixConnectionStatus,
    getMetrics: getApixMetrics,
    checkHealth: checkApixHealth
};