import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { AuthProvider } from "@/components/providers/auth-provider";
import { Toaster } from "@/components/ui/toaster";
import Script from "next/script";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "SynapseAI - Unified AI Orchestration Platform",
  description: "Build, deploy, and manage complex AI workflows through an intuitive no-code interface with enterprise-grade security and real-time capabilities.",
  keywords: ["AI", "Orchestration", "Workflow", "Automation", "Enterprise", "No-code"],
  authors: [{ name: "SynapseAI Team" }],
  creator: "SynapseAI",
  publisher: "SynapseAI",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://synapseai.com",
    title: "SynapseAI - Unified AI Orchestration Platform",
    description: "Build, deploy, and manage complex AI workflows through an intuitive no-code interface with enterprise-grade security and real-time capabilities.",
    siteName: "SynapseAI",
  },
  twitter: {
    card: "summary_large_image",
    title: "SynapseAI - Unified AI Orchestration Platform",
    description: "Build, deploy, and manage complex AI workflows through an intuitive no-code interface with enterprise-grade security and real-time capabilities.",
    creator: "@synapseai",
  },
};

// Separate viewport export as per Next.js recommendation
export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="msapplication-TileColor" content="#3b82f6" />
      </head>
      <body className={inter.className}>
        <Script
          src="https://api.tempo.build/proxy-asset?url=https://storage.googleapis.com/tempo-public-assets/error-handling.js"
          strategy="afterInteractive"
        />
        <AuthProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            {children}
            <Toaster />
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}