/**
 * Event Constants
 * Centralized event type definitions for real-time features
 */

/**
 * APIX Event Types
 * All available event types for the real-time event system
 */
export const APIX_EVENT_TYPES = {
    // Tool events
    TOOL_CALL_START: 'tool_call_start',
    TOOL_CALL_RESULT: 'tool_call_result',
    TOOL_CALL_ERROR: 'tool_call_error',
    TOOL_EXECUTED: 'tool_executed',

    // Agent events
    THINKING_STATUS: 'thinking_status',
    AGENT_STATUS: 'agent_status',
    AGENT_THINKING: 'agent_thinking',
    AGENT_RESPONSE: 'agent_response',

    // Session events
    TEXT_CHUNK: 'text_chunk',
    STATE_UPDATE: 'state_update',
    REQUEST_USER_INPUT: 'request_user_input',
    SESSION_START: 'session_start',
    SESSION_END: 'session_end',

    // Workflow events
    WORKFLOW_STARTED: 'workflow_started',
    WORKFLOW_COMPLETED: 'workflow_completed',
    WORKFLOW_FAILED: 'workflow_failed',

    // Provider events
    PROVIDER_HEALTH: 'provider_health',
    PROVIDER_FALLBACK: 'provider_fallback',

    // System events
    ERROR_OCCURRED: 'error_occurred',
    FALLBACK_TRIGGERED: 'fallback_triggered',
    SYSTEM_ALERT: 'system_alert',
    SYSTEM_MAINTENANCE: 'system_maintenance',

    // Admin events
    SECURITY_EVENT: 'security.event',
    ADMIN_EVENT: 'admin.event',
    ADMIN_USER_CREATED: 'admin_user_created',
    ADMIN_USER_UPDATED: 'admin_user_updated',

    // Organization events
    ORGANIZATION_EVENT: 'organization.event',
    ORGANIZATION_UPDATED: 'organization.updated',
    ORGANIZATION_CREATED: 'organization.created',

    // User events
    USER_ACTIVITY: 'user.activity'
};

/**
 * APIX Event Channels
 * All available event channels for the real-time event system
 */
export const APIX_CHANNELS = {
    AGENT_EVENTS: 'agent-events',
    TOOL_EVENTS: 'tool-events',
    WORKFLOW_EVENTS: 'workflow-events',
    PROVIDER_EVENTS: 'provider-events',
    SYSTEM_EVENTS: 'system-events',
    SESSION_EVENTS: 'session-events',
    ADMIN_EVENTS: 'admin-events',
    ORGANIZATION_EVENTS: 'organization-events',
    USER_EVENTS: 'user-events'
};

/**
 * Event Priority Levels
 */
export const EVENT_PRIORITY = {
    LOW: 'low',
    NORMAL: 'normal',
    HIGH: 'high',
    URGENT: 'urgent'
};

/**
 * Default APIX Configuration
 */
export const DEFAULT_APIX_CONFIG = {
    maxReconnectAttempts: 5,
    reconnectDelay: 2000,
    compressionThreshold: 10000,
    enableLatencyTracking: true,
    enableEventReplay: true,
    queueMaxSize: 100,
    enableCompression: true,
    heartbeatInterval: 30000,
    connectionTimeout: 10000,
    enableRetryLogic: true,
    maxEventHistory: 1000
};