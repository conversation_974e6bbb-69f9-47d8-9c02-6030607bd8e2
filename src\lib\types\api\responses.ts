/**
 * 🎯 API Response Types
 * Standardized response types for all API endpoints
 */

// Base API response interface
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    code?: string;
    message?: string;
}

// Paginated response with metadata
export interface PaginatedResponse<T = any> extends ApiResponse<T> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
}

// Authentication token response
export interface AuthTokenResponse {
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
    accessTokenExpiresAt: string;
    refreshTokenExpiresAt: string;
}

// List response with stats
export interface ListResponse<T = any, S = any> extends ApiResponse<T[]> {
    stats?: S;
    pagination?: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
}

// Error response with details
export interface ErrorResponse extends ApiResponse<never> {
    success: false;
    error: string;
    code: string;
    details?: any;
}

// Success response
export interface SuccessResponse<T = any> extends ApiResponse<T> {
    success: true;
    data?: T;
    message?: string;
}

// User response
export interface UserResponse {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    isActive: boolean;
    emailVerified: boolean;
    organizationId: string;
    organization: {
        id: string;
        name: string;
        slug: string;
    };
    permissions: string[];
    createdAt: string;
    updatedAt: string;
    lastLoginAt?: string;
    profile?: {
        phoneNumber?: string;
        jobTitle?: string;
        department?: string;
        timezone?: string;
    };
}

// Session response
export interface SessionResponse {
    user: UserResponse;
    expiresAt: string;
}

// Tool response
export interface ToolResponse {
    id: string;
    name: string;
    description: string;
    type: string;
    category: string;
    config: Record<string, any>;
    tags: string[];
    isPublic: boolean;
    createdBy: string;
    organizationId: string;
    createdAt: string;
    updatedAt: string;
}

// Tool execution response
export interface ToolExecutionResponse {
    id: string;
    toolId: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout';
    input: Record<string, any>;
    output?: any;
    error?: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    metadata?: Record<string, any>;
}

// Agent response
export interface AgentResponse {
    id: string;
    name: string;
    description: string;
    type: string;
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    tools: string[];
    isPublic: boolean;
    createdBy: string;
    organizationId: string;
    createdAt: string;
    updatedAt: string;
}

// Agent execution response
export interface AgentExecutionResponse {
    id: string;
    agentId: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'timeout';
    input: string;
    output?: string;
    error?: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    tokens?: {
        input: number;
        output: number;
        total: number;
    };
    metadata?: Record<string, any>;
}

// Workflow response
export interface WorkflowResponse {
    id: string;
    name: string;
    description: string;
    definition: {
        nodes: any[];
        edges: any[];
    };
    tags: string[];
    isPublic: boolean;
    createdBy: string;
    organizationId: string;
    createdAt: string;
    updatedAt: string;
}

// Workflow execution response
export interface WorkflowExecutionResponse {
    id: string;
    workflowId: string;
    status: 'pending' | 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';
    input: Record<string, any>;
    output?: any;
    error?: string;
    startedAt: string;
    completedAt?: string;
    duration?: number;
    nodeStatuses: Array<{
        nodeId: string;
        status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
        startedAt?: string;
        completedAt?: string;
        output?: any;
        error?: string;
    }>;
    metadata?: Record<string, any>;
}

// Organization response
export interface OrganizationResponse {
    id: string;
    name: string;
    slug: string;
    domain?: string;
    isActive: boolean;
    plan?: string;
    features?: string[];
    settings?: Record<string, any>;
    branding?: Record<string, any>;
    createdAt: string;
    updatedAt: string;
}

// System health response
export interface SystemHealthResponse {
    status: 'healthy' | 'degraded' | 'unhealthy';
    version: string;
    uptime: number;
    services: Record<string, {
        status: 'healthy' | 'degraded' | 'unhealthy';
        latency: number;
        message?: string;
    }>;
    resources: {
        cpu: number;
        memory: number;
        disk: number;
    };
    timestamp: string;
}