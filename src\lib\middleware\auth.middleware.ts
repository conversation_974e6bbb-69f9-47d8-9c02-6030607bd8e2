/**
 * Authentication Middleware
 * Production-grade middleware for handling authentication and authorization
 */

import { NextRequest, NextResponse } from 'next/server';
import {
    getAuthenticatedSession,
    hasRequiredRole,
    hasPermission,
    unauthorizedResponse,
    forbiddenResponse,
    errorResponse
} from '@/lib/auth/auth-service';
import { SessionUser, Session } from '@/lib/types/auth';
import { Role } from '@prisma/client';
import { PERMISSIONS } from '@/lib/constants/permissions';
import { API_ERROR_CODES, HTTP_STATUS } from '@/lib/constants/api';
import { mcp_Memory_create_entities } from '@/lib/mcp-tools';

// Extend NextRequest to include user and session
export interface AuthenticatedRequest extends NextRequest {
    user?: SessionUser;
    session?: Session;
}

/**
 * Middleware to check if user is authenticated
 */
export function withAuth(
    handler: (req: AuthenticatedRequest) => Promise<NextResponse>,
    options?: {
        requiredRoles?: Role[];
        requiredPermissions?: string[];
    }
) {
    return async (req: NextRequest) => {
        try {
            // Get authenticated session
            const session = await getAuthenticatedSession(req);

            // If no user and authentication is required
            if (!session) {
                return unauthorizedResponse();
            }

            // Check role-based access if roles are specified
            if (options?.requiredRoles && !hasRequiredRole(session.user.role, options.requiredRoles)) {
                // Log access denied in MCP Memory
                await mcp_Memory_create_entities({
                    entities: [
                        {
                            name: `access_denied:${session.user.id}:${Date.now()}`,
                            entityType: 'security_event',
                            observations: [
                                `Access denied for user ${session.user.firstName} ${session.user.lastName}`,
                                `Required roles: ${options.requiredRoles.join(', ')}`,
                                `User role: ${session.user.role}`,
                                `URL: ${req.url}`,
                                `Method: ${req.method}`,
                                `Time: ${new Date().toISOString()}`
                            ]
                        }
                    ]
                });

                return forbiddenResponse();
            }

            // Check permission-based access if permissions are specified
            if (options?.requiredPermissions) {
                const hasAllPermissions = options.requiredPermissions.every(
                    permission => hasPermission(session.user, permission)
                );

                if (!hasAllPermissions) {
                    // Log permission denied in MCP Memory
                    await mcp_Memory_create_entities({
                        entities: [
                            {
                                name: `permission_denied:${session.user.id}:${Date.now()}`,
                                entityType: 'security_event',
                                observations: [
                                    `Permission denied for user ${session.user.firstName} ${session.user.lastName}`,
                                    `Required permissions: ${options.requiredPermissions.join(', ')}`,
                                    `User permissions: ${session.user.permissions.join(', ')}`,
                                    `URL: ${req.url}`,
                                    `Method: ${req.method}`,
                                    `Time: ${new Date().toISOString()}`
                                ]
                            }
                        ]
                    });

                    return forbiddenResponse();
                }
            }

            // Add user and session to request
            const authenticatedReq = req as AuthenticatedRequest;
            authenticatedReq.user = session.user;
            authenticatedReq.session = session;

            // Call the handler with the authenticated request
            return handler(authenticatedReq);
        } catch (error) {
            console.error('Authentication middleware error:', error);
            return errorResponse('Authentication failed');
        }
    };
}

/**
 * Middleware to validate request body against schema
 */
export interface ValidatedRequest extends NextRequest {
    validatedBody?: any;
    validatedQuery?: any;
    validatedParams?: any;
}

export interface ValidationOptions {
    body?: any; // Zod schema
    query?: any; // Zod schema
    params?: any; // Zod schema
}

export function withValidation(
    handler: (req: ValidatedRequest) => Promise<NextResponse>,
    options: ValidationOptions
) {
    return function (req: NextRequest) {
        const validateRequest = async () => {
            try {
                const validatedReq = req as ValidatedRequest;

                // Validate request body if schema provided
                if (options.body && req.method !== 'GET') {
                    try {
                        const body = await req.json();
                        validatedReq.validatedBody = options.body.parse(body);
                    } catch (error: any) {
                        return NextResponse.json(
                            {
                                error: 'Validation failed',
                                code: API_ERROR_CODES.VALIDATION.INVALID_INPUT,
                                details: error.errors || [error.message]
                            },
                            { status: HTTP_STATUS.BAD_REQUEST }
                        );
                    }
                }

                // Validate query params if schema provided
                if (options.query) {
                    try {
                        const url = new URL(req.url);
                        const queryObj = Object.fromEntries(url.searchParams.entries());
                        validatedReq.validatedQuery = options.query.parse(queryObj);
                    } catch (error: any) {
                        return NextResponse.json(
                            {
                                error: 'Query validation failed',
                                code: API_ERROR_CODES.VALIDATION.INVALID_FORMAT,
                                details: error.errors || [error.message]
                            },
                            { status: HTTP_STATUS.BAD_REQUEST }
                        );
                    }
                }

                // Call the handler with the validated request
                return handler(validatedReq);
            } catch (error) {
                console.error('Validation middleware error:', error);
                return errorResponse('Validation failed');
            }
        };

        return validateRequest();
    };
}

/**
 * Combine auth and validation middleware
 */
export function withAuthAndValidation(
    handler: (req: AuthenticatedRequest & ValidatedRequest) => Promise<NextResponse>,
    authOptions?: {
        requiredRoles?: Role[];
        requiredPermissions?: string[];
    },
    validationOptions?: ValidationOptions
) {
    // First apply validation, then authentication
    const validatedHandler = validationOptions
        ? withValidation(handler as any, validationOptions)
        : handler;

    // Then apply authentication
    return withAuth(validatedHandler as any, authOptions);
}

// Re-export response helpers from auth-service
export { unauthorizedResponse, forbiddenResponse, errorResponse };