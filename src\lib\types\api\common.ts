/**
 * Common API Types
 * Shared types used across multiple API endpoints
 */

// HTTP Methods
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

// API Error
export interface ApiError {
    message: string;
    code: string;
    status: number;
    details?: any;
}

// API Headers
export interface ApiHeaders {
    [key: string]: string;
}

// API Client Configuration
export interface ApiClientConfig {
    baseURL: string;
    timeout?: number;
    retries?: number;
    retryDelay?: number;
    headers?: ApiHeaders;
}

// Tool Types
export type ToolType =
    | 'database'
    | 'api'
    | 'browser'
    | 'file'
    | 'shell'
    | 'custom'
    | 'integration';

// Tool Categories
export type ToolCategory =
    | 'data_processing'
    | 'communication'
    | 'analysis'
    | 'automation'
    | 'integration'
    | 'custom';

// Agent Types
export type AgentType =
    | 'standalone'
    | 'tool_driven'
    | 'multi_tasking'
    | 'hybrid';

// Agent Provider
export type AgentProvider =
    | 'openai'
    | 'anthropic'
    | 'google'
    | 'mistral'
    | 'custom';

// Workflow Node Types
export type WorkflowNodeType =
    | 'tool'
    | 'agent'
    | 'condition'
    | 'transform'
    | 'input'
    | 'output'
    | 'loop'
    | 'human_input'
    | 'custom';

// Status Types
export type ExecutionStatus =
    | 'pending'
    | 'running'
    | 'completed'
    | 'failed'
    | 'timeout'
    | 'cancelled'
    | 'paused';

// User Status
export type UserStatus =
    | 'active'
    | 'suspended'
    | 'inactive'
    | 'pending';

// Organization Status
export type OrganizationStatus =
    | 'active'
    | 'suspended'
    | 'inactive'
    | 'trial'
    | 'expired';

// Theme Options
export type ThemeOption = 'light' | 'dark' | 'system';

// View Mode
export type ViewMode = 'grid' | 'list' | 'table';

// Sort Direction
export type SortDirection = 'asc' | 'desc';

// Date Range
export type DateRange = '7d' | '30d' | '90d' | '180d' | '365d' | 'custom';

// Notification Type
export type NotificationType = 'info' | 'success' | 'warning' | 'error';

// Permission Level
export type PermissionLevel = 'read' | 'write' | 'admin' | 'none';

// Utility Types
export type UUID = string;
export type ISODateString = string;
export type JSONValue = string | number | boolean | null | { [key: string]: JSONValue } | JSONValue[];
export type JSONObject = { [key: string]: JSONValue };
export type JSONArray = JSONValue[];