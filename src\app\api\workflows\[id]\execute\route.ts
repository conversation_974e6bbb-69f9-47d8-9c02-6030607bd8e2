import { PrismaClient, Role } from "@prisma/client";
import { WorkflowDefinition, WorkflowStepInput } from "@/lib/types/workflow";
import { fromPrismaJson } from "@/lib/types/prisma";
import { ExecutionStatus } from "@prisma/client";
import { createPost<PERSON>and<PERSON> } from "@/lib/utils/api-handler";
import { successResponse } from "@/lib/utils/api-responses";
import { z } from "zod";
import { PERMISSIONS } from "@/lib/constants/permissions";

const prisma = new PrismaClient();

// Validation schema for params
const paramsSchema = z.object({
    id: z.string().uuid("Invalid workflow ID"),
});

// POST /api/workflows/[id]/execute - Execute workflow
export const POST = createPostHandler(
    async (req, context) => {
        const { id: workflowId } = req.validatedParams;
        const session = req.user;

        // Check if workflow exists and user has access
        const workflow = await prisma.workflow.findFirst({
            where: {
                id: workflowId,
                organizationId: session.organizationId,
                isActive: true,
            },
        });

        if (!workflow) {
            return successResponse(null, "Workflow not found or inactive", 404);
        }

        // Get or create session
        let userSession = await prisma.session.findFirst({
            where: {
                userId: session.id,
                isActive: true,
            },
        });

        if (!userSession) {
            userSession = await prisma.session.create({
                data: {
                    userId: session.id,
                    organizationId: session.organizationId,
                    sessionData: {},
                    context: {},
                    memory: {},
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
                },
            });
        }

        // Create workflow execution
        const execution = await prisma.workflowExecution.create({
            data: {
                workflowId: workflow.id,
                sessionId: userSession.id,
                status: "PENDING",
                input: {
                    triggeredBy: session.id,
                    triggerType: "manual",
                    timestamp: new Date().toISOString(),
                },
            },
        });

        // Create initial workflow steps based on definition
        const definition = fromPrismaJson<WorkflowDefinition>(workflow.definition);
        const steps: WorkflowStepInput[] = [];

        if (definition?.nodes && Array.isArray(definition.nodes)) {
            for (let i = 0; i < definition.nodes.length; i++) {
                const node = definition.nodes[i];
                steps.push({
                    stepId: node.id || `step-${i}`,
                    name: node.data?.label || `Step ${i + 1}`,
                    type: node.type || "action",
                    status: "PENDING" as ExecutionStatus,
                    input: node.data || {},
                    executionId: execution.id,
                });
            }
        }

        if (steps.length > 0) {
            await prisma.workflowStep.createMany({
                data: steps,
            });
        }

        // Start execution (in a real implementation, this would trigger the workflow engine)
        await prisma.workflowExecution.update({
            where: { id: execution.id },
            data: {
                status: "RUNNING",
                startedAt: new Date(),
            },
        });

        // Simulate workflow execution (replace with actual workflow engine)
        setTimeout(async () => {
            try {
                // Update first step
                if (steps.length > 0) {
                    await prisma.workflowStep.updateMany({
                        where: { executionId: execution.id, stepId: steps[0].stepId },
                        data: {
                            status: "RUNNING",
                            startedAt: new Date(),
                        },
                    });

                    // Simulate processing time
                    setTimeout(async () => {
                        // Complete steps one by one
                        for (let i = 0; i < steps.length; i++) {
                            const step = steps[i];
                            const stepStartTime = Date.now();

                            await prisma.workflowStep.updateMany({
                                where: { executionId: execution.id, stepId: step.stepId },
                                data: {
                                    status: "RUNNING",
                                    startedAt: new Date(),
                                },
                            });

                            // Simulate step processing
                            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

                            // Complete step
                            const stepEndTime = Date.now();
                            await prisma.workflowStep.updateMany({
                                where: { executionId: execution.id, stepId: step.stepId },
                                data: {
                                    status: "COMPLETED",
                                    completedAt: new Date(),
                                    duration: stepEndTime - stepStartTime,
                                    output: { message: `Step ${i + 1} completed successfully` },
                                },
                            });
                        }

                        // Complete execution
                        const executionEndTime = Date.now();
                        const executionStartTime = new Date(execution.startedAt || Date.now()).getTime();

                        await prisma.workflowExecution.update({
                            where: { id: execution.id },
                            data: {
                                status: "COMPLETED",
                                completedAt: new Date(),
                                duration: executionEndTime - executionStartTime,
                                output: { message: "Workflow completed successfully" },
                            },
                        });

                        // Send notification (if notifications service is available)
                        try {
                            await fetch(`${process.env.NEXTAUTH_URL}/api/notifications`, {
                                method: "POST",
                                headers: { "Content-Type": "application/json" },
                                body: JSON.stringify({
                                    type: "websocket",
                                    title: "Workflow Completed",
                                    message: `Workflow "${workflow.name}" has completed successfully`,
                                    userId: session.id,
                                    organizationId: session.organizationId,
                                    priority: "medium",
                                    data: {
                                        workflowId: workflow.id,
                                        executionId: execution.id,
                                        status: "completed",
                                    },
                                }),
                            });
                        } catch (notifError) {
                            console.warn("Failed to send notification:", notifError);
                        }
                    }, 500);
                }
            } catch (error) {
                console.error("Workflow execution failed:", error);

                // Mark execution as failed
                await prisma.workflowExecution.update({
                    where: { id: execution.id },
                    data: {
                        status: "FAILED",
                        completedAt: new Date(),
                        error: error instanceof Error ? error.message : "Unknown error",
                    },
                });
            }
        }, 100);

        // Create audit log
        await prisma.auditLog.create({
            data: {
                userId: session.id,
                action: "WORKFLOW_EXECUTED",
                resource: "WORKFLOW",
                resourceId: workflow.id,
                details: {
                    executionId: execution.id,
                    workflowName: workflow.name,
                },
                organizationId: session.organizationId,
            },
        });

        return successResponse({
            executionId: execution.id,
            status: "started",
        }, "Workflow execution started successfully");
    },
    {
        paramsSchema,
        requiredPermissions: [PERMISSIONS.WORKFLOW_EXECUTE as unknown as string],
        requiredRoles: ['SUPER_ADMIN' as Role],
    }
);