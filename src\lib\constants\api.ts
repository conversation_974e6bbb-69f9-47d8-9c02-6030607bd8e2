/**
 * API Constants
 * Centralized API endpoint definitions for consistent usage across the application
 */

export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    SESSION: '/auth/session',
    WS_TOKEN: '/auth/ws-token',
  },
  
  // User endpoints
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    PREFERENCES: '/users/preferences',
    SETTINGS: '/users/settings',
  },
  
  // Organization endpoints
  ORGANIZATIONS: {
    BASE: '/organizations',
    SETTINGS: '/organizations/settings',
    USERS: '/organizations/users',
    INVITATIONS: '/organizations/invitations',
    USAGE: '/organizations/usage',
    LIST: '/organizations/list',
  },
  
  // Admin endpoints
  ADMIN: {
    USERS: '/admin/users',
    ORGANIZATIONS: '/admin/organizations',
    SYSTEM: '/admin/system',
  },
  
  // Tool endpoints
  TOOLS: {
    BASE: '/tools',
    CATEGORIES: '/tools/categories',
    POPULAR: '/tools/popular',
    STATS: '/tools/stats',
    EXECUTE: (id: string) => `/tools/${id}/execute`,
    EXPORT: (id: string) => `/tools/${id}/export`,
    EXECUTIONS: '/tools/executions',
    EXECUTION: (id: string) => `/tools/executions/${id}`,
  },
  
  // Agent endpoints
  AGENTS: {
    BASE: '/agents',
    TEMPLATES: '/agents/templates',
    EXECUTE: (id: string) => `/agents/${id}/execute`,
    SESSIONS: (id: string) => `/agents/${id}/sessions`,
    SESSION: (id: string, sessionId: string) => `/agents/${id}/sessions/${sessionId}`,
  },
  
  // Workflow endpoints
  WORKFLOWS: {
    BASE: '/workflows',
    TEMPLATES: '/workflows/templates',
    EXECUTE: (id: string) => `/workflows/${id}/execute`,
    EXECUTIONS: '/workflows/executions',
    EXECUTION: (id: string, executionId: string) => `/workflows/${id}/executions/${executionId}`,
    NODES: '/workflows/nodes',
  },
  
  // Provider endpoints
  PROVIDERS: {
    BASE: '/providers',
    TEST: (id: string) => `/providers/${id}/test`,
    HEALTH: (id: string) => `/providers/${id}/health`,
  },
  
  // System endpoints
  SYSTEM: {
    HEALTH: '/system/health',
    STATUS: '/system/status',
    METRICS: '/system/metrics',
    LOGS: '/system/logs',
  },
  
  // MCP endpoints
  MCP: {
    MEMORY: {
      ENTITIES: '/mcp/memory/entities',
      RELATIONS: '/mcp/memory/relations',
      SEARCH: '/mcp/memory/search',
    },
    PLAYWRIGHT: {
      SESSION: '/mcp/playwright/session',
      NAVIGATE: '/mcp/playwright/navigate',
      SCREENSHOT: '/mcp/playwright/screenshot',
    }
  }
};

/**
 * HTTP Status Codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
};

/**
 * API Error Codes
 */
export const API_ERROR_CODES = {
  AUTHENTICATION: {
    INVALID_CREDENTIALS: 'auth/invalid-credentials',
    TOKEN_EXPIRED: 'auth/token-expired',
    INVALID_TOKEN: 'auth/invalid-token',
    UNAUTHORIZED: 'auth/unauthorized',
    MFA_REQUIRED: 'auth/mfa-required',
    EMAIL_NOT_VERIFIED: 'auth/email-not-verified',
  },
  VALIDATION: {
    INVALID_INPUT: 'validation/invalid-input',
    MISSING_FIELD: 'validation/missing-field',
    INVALID_FORMAT: 'validation/invalid-format',
  },
  RESOURCE: {
    NOT_FOUND: 'resource/not-found',
    ALREADY_EXISTS: 'resource/already-exists',
    CONFLICT: 'resource/conflict',
  },
  PERMISSION: {
    FORBIDDEN: 'permission/forbidden',
    INSUFFICIENT_ROLE: 'permission/insufficient-role',
    INSUFFICIENT_PERMISSION: 'permission/insufficient-permission',
  },
  SYSTEM: {
    INTERNAL_ERROR: 'system/internal-error',
    SERVICE_UNAVAILABLE: 'system/service-unavailable',
    RATE_LIMIT_EXCEEDED: 'system/rate-limit-exceeded',
  },
};

/**
 * API Request Methods
 */
export const API_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

/**
 * Default API Request Headers
 */
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'X-Client-Version': '1.0.0',
};

/**
 * API Request Timeout (in milliseconds)
 */
export const API_TIMEOUT = 30000; // 30 seconds

/**
 * API Retry Configuration
 */
export const API_RETRY = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
};