import { createGet<PERSON><PERSON><PERSON>, createPostHandler } from '@/lib/utils/api-handler';
import { successResponse, createdResponse } from '@/lib/utils/api-responses';
import { z } from 'zod';
import apiClient from '@/lib/api-client';

// GET /api/tools/categories - Get tool categories
export const GET = createGetHandler(
  async () => {
    const categories = await apiClient.get('/tools/categories');
    return successResponse(categories, "Tool categories fetched successfully");
  },
  {
    requiredPermissions: ['tool:read'],
  }
);

// Category creation schema
const createCategorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
});

// POST /api/tools/categories - Create a new category
export const POST = createPostHandler(
  async (req) => {
    const categoryData = req.validatedBody;

    const category = await apiClient.post('/tools/categories', categoryData);
    return createdResponse(category, "Category created successfully");
  },
  {
    bodySchema: createCategorySchema,
    requiredPermissions: ['tool:create'],
  }
);