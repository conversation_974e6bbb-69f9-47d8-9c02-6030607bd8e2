/**
 * Role Constants
 * Centralized role definitions for consistent usage across the application
 */

import { Role } from '@prisma/client';

/**
 * Role hierarchy for permission checking
 */
export const ROLE_HIERARCHY: Record<Role, number> = {
    SUPER_ADMIN: 100,
    ORG_ADMIN: 80,
    ADMIN: 80, // Same level as ORG_ADMIN for compatibility
    DEVELOPER: 60,
    VIEWER: 40,
    USER: 20
};

/**
 * Role display names for UI presentation
 */
export const ROLE_DISPLAY_NAMES: Record<Role, string> = {
    SUPER_ADMIN: 'Super Admin',
    ORG_ADMIN: 'Organization Admin',
    ADMIN: 'Admin',
    DEVELOPER: 'Developer',
    VIEWER: 'Viewer',
    USER: 'User'
};

/**
 * Role descriptions for UI presentation
 */
export const ROLE_DESCRIPTIONS: Record<Role, string> = {
    SUPER_ADMIN: 'Full access to all system features and organizations',
    ORG_ADMIN: 'Full access to organization features and user management',
    ADMIN: 'Full access to organization features and user management',
    DEVELOPER: 'Create and manage tools, agents, and workflows',
    VIEWER: 'View and execute existing tools, agents, and workflows',
    USER: 'Basic access to assigned resources'
};

/**
 * Role colors for UI presentation
 */
export const ROLE_COLORS: Record<Role, string> = {
    SUPER_ADMIN: 'red',
    ORG_ADMIN: 'orange',
    ADMIN: 'orange',
    DEVELOPER: 'blue',
    VIEWER: 'green',
    USER: 'gray'
};

/**
 * Check if a role has higher or equal privileges than required roles
 */
export function hasRequiredRole(userRole: Role, requiredRoles: Role[]): boolean {
    // Super admin always has access
    if (userRole === 'SUPER_ADMIN') return true;

    // Get user's role level
    const userLevel = ROLE_HIERARCHY[userRole];

    // Check if user's role level meets any of the required role levels
    return requiredRoles.some(role => userLevel >= ROLE_HIERARCHY[role]);
}