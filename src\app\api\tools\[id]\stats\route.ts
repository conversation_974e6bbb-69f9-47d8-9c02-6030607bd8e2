import { createGetHandler } from '@/lib/utils/api-handler';
import { successResponse } from '@/lib/utils/api-responses';
import { z } from 'zod';
import apiClient from '@/lib/api-client';

// Validation schema for params
const paramsSchema = z.object({
  id: z.string().uuid('Invalid tool ID'),
});

// Query validation schema for time range
const querySchema = z.object({
  days: z.string().optional().transform(val => val ? parseInt(val) : 30),
  granularity: z.enum(['hour', 'day']).optional().default('day'),
});

// GET /api/tools/[id]/stats - Get statistics for a specific tool
export const GET = createGetHandler(
  async (req) => {
    const { id } = req.validatedParams;
    const { days, granularity } = req.validatedQuery;
    
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (days) queryParams.append('days', days.toString());
    if (granularity) queryParams.append('granularity', granularity);
    
    const stats = await apiClient.get(`/tools/${id}/analytics?${queryParams.toString()}`);
    return successResponse(stats, "Tool statistics fetched successfully");
  },
  {
    paramsSchema,
    querySchema,
    requiredPermissions: ['tool:read'],
  }
);
