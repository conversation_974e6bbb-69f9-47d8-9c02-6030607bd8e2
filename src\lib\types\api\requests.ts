/**
 * API Request Types
 * Standardized request types for all API endpoints
 */

import { z } from 'zod';

// Common pagination parameters
export interface PaginationParams {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

// Common search parameters
export interface SearchParams extends PaginationParams {
    query?: string;
    filters?: Record<string, any>;
}

// Authentication requests
export interface LoginRequest {
    email: string;
    password: string;
    organizationSlug?: string;
    mfaCode?: string;
    rememberMe?: boolean;
}

export interface RegisterRequest {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    company: string;
    subscribeNewsletter?: boolean;
}

export interface RefreshTokenRequest {
    refreshToken?: string;
}

// User requests
export interface UpdateUserRequest {
    firstName?: string;
    lastName?: string;
    email?: string;
    role?: string;
    status?: string;
    organizationId?: string;
    profile?: {
        phoneNumber?: string;
        jobTitle?: string;
        department?: string;
        timezone?: string;
    };
}

export interface UpdateUserPreferencesRequest {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    notifications?: {
        email?: boolean;
        push?: boolean;
        sms?: boolean;
    };
    displayOptions?: {
        density?: 'compact' | 'comfortable' | 'spacious';
        colorScheme?: string;
        fontSize?: number;
    };
}

// Tool requests
export interface CreateToolRequest {
    name: string;
    description: string;
    type: string;
    category: string;
    config: Record<string, any>;
    tags?: string[];
    isPublic?: boolean;
}

export interface UpdateToolRequest {
    name?: string;
    description?: string;
    type?: string;
    category?: string;
    config?: Record<string, any>;
    tags?: string[];
    isPublic?: boolean;
}

export interface ExecuteToolRequest {
    input: Record<string, any>;
    options?: {
        timeout?: number;
        async?: boolean;
        cache?: boolean;
        cacheExpiry?: number;
    };
}

// Agent requests
export interface CreateAgentRequest {
    name: string;
    description: string;
    type: string;
    provider: string;
    model: string;
    temperature: number;
    maxTokens: number;
    systemPrompt: string;
    tools?: string[];
    templateId?: string;
    isPublic?: boolean;
}

export interface UpdateAgentRequest {
    name?: string;
    description?: string;
    type?: string;
    provider?: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    systemPrompt?: string;
    tools?: string[];
    isPublic?: boolean;
}

export interface ExecuteAgentRequest {
    input: string;
    systemPrompt?: string;
    maxTokens?: number;
    temperature?: number;
    variables?: Record<string, any>;
}

// Workflow requests
export interface CreateWorkflowRequest {
    name: string;
    description: string;
    definition: {
        nodes: any[];
        edges: any[];
    };
    tags?: string[];
    isPublic?: boolean;
}

export interface UpdateWorkflowRequest {
    name?: string;
    description?: string;
    definition?: {
        nodes: any[];
        edges: any[];
    };
    tags?: string[];
    isPublic?: boolean;
}

export interface ExecuteWorkflowRequest {
    input: Record<string, any>;
    options?: {
        async?: boolean;
        timeout?: number;
        debug?: boolean;
    };
}

// Zod schemas for validation
export const Schemas = {
    Login: z.object({
        email: z.string().email('Invalid email address'),
        password: z.string().min(8, 'Password must be at least 8 characters'),
        organizationSlug: z.string().optional(),
        mfaCode: z.string().optional(),
        rememberMe: z.boolean().optional()
    }),

    Register: z.object({
        firstName: z.string().min(1, 'First name is required'),
        lastName: z.string().min(1, 'Last name is required'),
        email: z.string().email('Invalid email address'),
        password: z.string().min(8, 'Password must be at least 8 characters'),
        company: z.string().min(1, 'Company name is required'),
        subscribeNewsletter: z.boolean().optional()
    }),

    UpdateUser: z.object({
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        email: z.string().email('Invalid email address').optional(),
        role: z.string().optional(),
        status: z.string().optional(),
        organizationId: z.string().optional(),
        profile: z.object({
            phoneNumber: z.string().optional(),
            jobTitle: z.string().optional(),
            department: z.string().optional(),
            timezone: z.string().optional()
        }).optional()
    }),

    CreateTool: z.object({
        name: z.string().min(1, 'Tool name is required'),
        description: z.string(),
        type: z.string(),
        category: z.string(),
        config: z.record(z.string(), z.any()),
        tags: z.array(z.string()).optional(),
        isPublic: z.boolean().optional()
    }),

    ExecuteTool: z.object({
        input: z.record(z.string(), z.any()),
        options: z.object({
            timeout: z.number().optional(),
            async: z.boolean().optional(),
            cache: z.boolean().optional(),
            cacheExpiry: z.number().optional()
        }).optional()
    }),

    PaginationQuery: z.object({
        page: z.string().optional().transform(val => val ? parseInt(val) : 1),
        limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
        sortBy: z.string().optional(),
        sortOrder: z.enum(['asc', 'desc'] as const).optional()
    }),

    SearchQuery: z.object({
        query: z.string().optional(),
        page: z.string().optional().transform(val => val ? parseInt(val) : 1),
        limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
        sortBy: z.string().optional(),
        sortOrder: z.enum(['asc', 'desc'] as const).optional()
    })
};